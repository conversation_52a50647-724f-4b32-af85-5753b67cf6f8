import { addFamily<PERSON>ember } from "@/api/profile-service";
import { useGetPaymentMethods, setDefaultPaymentMethod } from "@/api/payment-service";
import { extractErrorMessage } from "@/libs/utils";
import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface AddMemberDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onMemberAdded: () => void;
}

interface MemberFormData {
  name: string;
  email: string;
  dob: string;
  phone: string;
  country: string;
  paymentMethodId: string;
  makeDefault: boolean;
}

const AddMemberDialog: React.FC<AddMemberDialogProps> = ({ isOpen, onClose, onMemberAdded }) => {
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [memberFormData, setMemberFormData] = useState<MemberFormData>({
    name: "",
    email: "",
    dob: "",
    phone: "",
    country: "+1",
    paymentMethodId: "",
    makeDefault: false,
  });

  const { paymentMethodList, paymentMethodLoading } = useGetPaymentMethods();

  const handleMemberFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsAddingMember(true);
    try {
      const response = await addFamilyMember({
        name: memberFormData.name,
        email: memberFormData.email,
        phone: memberFormData.phone,
        country: memberFormData.country,
        dob: memberFormData.dob,
        paymentMethodId: memberFormData.paymentMethodId,
      });

      if (memberFormData.makeDefault && memberFormData.paymentMethodId) {
        await setDefaultPaymentMethod({ paymentMethodId: memberFormData.paymentMethodId });
      }

      if (response) {
        toast.success("Family member added successfully!");
        onMemberAdded();
        onClose();
        setMemberFormData({
          name: "",
          email: "",
          dob: "",
          phone: "",
          country: "+1",
          paymentMethodId: "",
          makeDefault: false,
        });
      }
    } catch (error: any) {
      console.error("Error adding member:", error);
      const errorMessage = extractErrorMessage(error);
      toast.error(errorMessage || "Failed to add family member");
    } finally {
      setIsAddingMember(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      onClose();
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    if (value.length <= 10) {
      setMemberFormData({ ...memberFormData, phone: value });
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
        <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-full max-w-lg translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-5 shadow-lg duration-200">
          <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-sm opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
            <X className="h-8 w-8 text-gray-600 outline-none" />
            <span className="sr-only">Close</span>
          </Dialog.Close>

          <Dialog.Title className="text-lg font-medium text-gray-900">
            Add Family Member
          </Dialog.Title>
          <Dialog.Description className="mb-4 text-sm text-gray-500">
            Add a new family member to your account.
          </Dialog.Description>

          <form onSubmit={handleMemberFormSubmit} className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Name</label>
                <input
                  type="text"
                  className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                  value={memberFormData.name}
                  onChange={(e) => setMemberFormData({ ...memberFormData, name: e.target.value })}
                  required
                  placeholder="Enter Full Name"
                  disabled={isAddingMember}
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <input
                  type="email"
                  className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                  value={memberFormData.email}
                  onChange={(e) => setMemberFormData({ ...memberFormData, email: e.target.value })}
                  required
                  placeholder="Enter Email"
                  disabled={isAddingMember}
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Date of Birth</label>
                <input
                  type="date"
                  className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                  value={memberFormData.dob}
                  onChange={(e) => setMemberFormData({ ...memberFormData, dob: e.target.value })}
                  required
                  placeholder="Enter Date of Birth"
                  min="1900-01-01"
                  max={new Date(Date.now() - 86400000).toISOString().split("T")[0]}
                  disabled={isAddingMember}
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                <div className="flex overflow-hidden rounded-full border border-gray-300">
                  <select
                    value={memberFormData.country}
                    onChange={(e) =>
                      setMemberFormData({ ...memberFormData, country: e.target.value })
                    }
                    className="border-r border-gray-300 bg-gray-50 px-3 py-3 text-sm focus:outline-none"
                    disabled={isAddingMember}
                  >
                    <option value="+1">🇺🇸 +1</option>
                  </select>
                  <input
                    type="tel"
                    placeholder="Mobile Number"
                    value={memberFormData.phone}
                    onChange={handlePhoneChange}
                    maxLength={10}
                    className="flex-1 px-3 py-3 text-black focus:outline-none"
                    disabled={isAddingMember}
                  />
                </div>
              </div>

              <div className="space-y-2 md:col-span-2">
                <label className="block text-sm font-medium text-gray-700">Payment Method</label>
                <select
                  className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                  value={memberFormData.paymentMethodId}
                  onChange={(e) =>
                    setMemberFormData({ ...memberFormData, paymentMethodId: e.target.value })
                  }
                  disabled={isAddingMember || paymentMethodLoading}
                  required
                >
                  <option value="">Select Payment Method</option>
                  {paymentMethodList.map((method: any) => (
                    <option key={method.id} value={method.id}>
                      {method.card?.brand.toUpperCase()} **** **** **** {method.card?.last4}
                    </option>
                  ))}
                </select>
                {paymentMethodLoading && (
                  <p className="text-sm text-gray-500">Loading payment methods...</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    className="focus:ring-primary text-primary h-4 w-4 rounded border-gray-300 focus:ring-2 focus:ring-offset-0"
                    checked={memberFormData.makeDefault}
                    onChange={(e) =>
                      setMemberFormData({ ...memberFormData, makeDefault: e.target.checked })
                    }
                    disabled={isAddingMember || !memberFormData.paymentMethodId}
                  />
                  <span className="text-sm text-gray-700">
                    Make this the default payment method
                  </span>
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                className="focus:ring-primary rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none"
                onClick={onClose}
                disabled={isAddingMember}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                disabled={isAddingMember}
              >
                {isAddingMember ? "Adding Member..." : "Add Member"}
              </button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default AddMemberDialog;
