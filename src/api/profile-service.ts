import axios from "axios";
import axiosInstance, { endpoints } from "./axios";

export interface FamilyMember {
  id: string;
  user: {
    name: string;
  };
  amount_paid: string;
  start_date: string;
  end_date: string;
}

export interface AddMemberData {
  name: string;
  email: string;
  phone: string;
  country: string;
  dob: string;
}

export interface AddCorporateData {
  name: string;
  email: string;
}

export interface ProfileImageResponse {
  success: boolean;
  message: string;
  user: {
    profile_image: {
      path: string;
    };
  };
}

// Stripe portal management
export const openStripePortal = async (): Promise<void> => {
  try {
    const response = await axiosInstance.get("/mob_app/auth/stripe/managePlan");
    console.log("stripe portal response", response.data);
    const portalURL = response.data.data.url;

    if (!portalURL) {
      throw new Error("No portal URL provided");
    }

    if (!window) {
      throw new Error("Window is not defined");
    }

    window.location.href = portalURL;

    // window.open(portalURL, "_blank", "noopener,noreferrer");
    // console.log("new tab", newTab);
    // if (newTab) {
    //   newTab.opener = null;
    //   newTab.focus();
    // } else {
    // window.location.href = portalURL;
    // }
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "An error occurred opening the portal");
  }
};

// Get family members
export const getFamilyMembers = async (): Promise<FamilyMember[]> => {
  try {
    const response = await axiosInstance.get("/mob_app/auth/subscribe/getFamilyMember");
    return response.data.data || [];
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to fetch family members");
  }
};

// Add family member
export const addFamilyMember = async (memberData: AddMemberData): Promise<any> => {
  const response = axiosInstance.post(
    "/mob_app/auth/subscribe/addFamilyMemberNew",
    memberData
  );
  return response;
};

export const addCorporateMember = async (memberData: AddCorporateData): Promise<any> => {
  const response = axiosInstance.post(
    "mob_app/auth/subscribe/addCorporateMember",
    memberData
  );
  return response;
};

// Upload profile image
export const uploadProfileImage = async (file: File): Promise<ProfileImageResponse> => {
  if (file.size > 4 * 1024 * 1024) {
    throw new Error("Image size must be less than 4MB");
  }

  const formData = new FormData();
  formData.append("profile_image", file);

  try {
    const response = await axiosInstance.post("/mob_app/auth/user/updateUserProfile", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to upload profile image");
  }
};

export async function updateUserProfile(data: any): Promise<any> {
  const response = await axiosInstance.post(endpoints.auth.updateUserProfile, data);
  console.log("update user profile response", response);
  return response;
}

export async function purchaseSetup(data: any): Promise<any> {
  const response = await axiosInstance.post(endpoints.auth.purchaseSetup, data);
  console.log("purchase setup response", response.data);
  return response.data;
}

export async function updateUserEmailOrPhone(data: any): Promise<any> {
  const response = await axiosInstance.post(endpoints.auth.updateUserEmailOrPhone, data);
  console.log("update user email or phone response", response);
  return response;
}

export async function updateUserVerifyEmailOrPhone(data: any): Promise<any> {
  const response = await axiosInstance.post(endpoints.auth.updateUserVerifyEmailOrPhone, data);
  console.log("update user verify email or phone response", response);
  return response;
}

export async function removeProfileImage(): Promise<any> {
  const response = await axiosInstance.post(endpoints.auth.removeProfileImage);
  console.log("remove profile image response", response);
  return response;
}
